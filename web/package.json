{"name": "tinderop", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "typecheck": "tsc --noEmit", "deploy": "bun run build && wrangler deploy", "deploy:preview": "bun run build && wrangler deploy --dry-run", "wrangler": "wrangler", "lint": "biome check src", "lint:fix": "biome check src --write", "format": "biome format src --write", "check": "biome check src --verbose", "check:fix": "biome check src --write --verbose", "cf-typegen": "wrangler types --env-interface Env"}, "dependencies": {"@clerk/tanstack-react-start": "^0.20.4", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tanstack/react-router": "^1.128.0", "@tanstack/react-start": "^1.128.1", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "framer-motion": "latest", "lucide-react": "^0.454.0", "openai": "^5.10.1", "react": "^19", "react-dom": "^19", "react-dropzone": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "latest", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@cloudflare/vite-plugin": "^1.7.5", "@types/node": "^22", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-oxc": "^0.2.3", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "npm:rolldown-vite@latest", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.22.0"}}
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON>, Sparkles, Star, Target, TrendingUp, Users } from "lucide-react";
import { motion } from "framer-motion";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep1Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep1({ onComplete, wizardData, isCompleted }: WizardStep1Props) {
  const [hasSeenWelcome, setHasSeenWelcome] = useState(wizardData.hasSeenWelcome || false);

  useEffect(() => {
    if (hasSeenWelcome && !isCompleted) {
      onComplete({ hasSeenWelcome: true });
    }
  }, [hasSeenWelcome, isCompleted, onComplete]);

  const features = [
    {
      icon: Target,
      title: "AI-Powered Analysis",
      description: "Get detailed feedback on your photos and bio from advanced AI models",
    },
    {
      icon: TrendingUp,
      title: "Proven Results",
      description: "Our optimization techniques have helped thousands get more matches",
    },
    {
      icon: Users,
      title: "Personalized Advice",
      description: "Tailored recommendations based on your goals and dating platform",
    },
  ];

  const handleGetStarted = () => {
    setHasSeenWelcome(true);
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-6">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex justify-center"
        >
          <div className="relative">
            <Heart className="w-16 h-16 text-flame-red fill-current" />
            <Sparkles className="w-6 h-6 text-sparks-pink absolute -top-1 -right-1" />
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-4"
        >
          <h1 className="text-3xl md:text-4xl font-bold text-graphite-90">
            Welcome to TinderOP
          </h1>
          <p className="text-lg text-graphite-60 max-w-2xl mx-auto">
            Transform your dating profile from overlooked to overbooked. 
            Our AI-powered platform will help you optimize every aspect of your profile 
            to get more matches and better conversations.
          </p>
        </motion.div>
      </div>

      {/* Features Grid */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
        className="grid md:grid-cols-3 gap-6"
      >
        {features.map((feature, index) => (
          <Card key={index} className="border border-graphite-20 hover:border-flame-red/30 transition-colors">
            <CardContent className="p-6 text-center space-y-4">
              <div className="flex justify-center">
                <div className="p-3 rounded-full bg-flame-red/10">
                  <feature.icon className="w-6 h-6 text-flame-red" />
                </div>
              </div>
              <h3 className="font-semibold text-graphite-90">{feature.title}</h3>
              <p className="text-sm text-graphite-60">{feature.description}</p>
            </CardContent>
          </Card>
        ))}
      </motion.div>

      {/* What to Expect */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="bg-gradient-to-r from-flame-red/5 to-sparks-pink/5 rounded-lg p-6"
      >
        <h3 className="font-semibold text-graphite-90 mb-4 flex items-center gap-2">
          <Star className="w-5 h-5 text-flame-red" />
          What to Expect
        </h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm text-graphite-70">
          <div className="space-y-2">
            <p>✓ Set your dating goals and preferences</p>
            <p>✓ Upload and analyze your photos</p>
            <p>✓ Optimize your bio for maximum appeal</p>
          </div>
          <div className="space-y-2">
            <p>✓ Get personalized improvement tips</p>
            <p>✓ Learn what works on your platform</p>
            <p>✓ Start getting better matches</p>
          </div>
        </div>
      </motion.div>

      {/* CTA */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="text-center"
      >
        <Button
          onClick={handleGetStarted}
          size="lg"
          className="px-8 py-3 text-lg"
          disabled={hasSeenWelcome}
        >
          {hasSeenWelcome ? "Ready to Continue" : "Let's Get Started"}
          <Sparkles className="ml-2 h-5 w-5" />
        </Button>
        
        {hasSeenWelcome && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-sm text-graphite-60 mt-3"
          >
            Click "Next" to continue to goal setting
          </motion.p>
        )}
      </motion.div>
    </div>
  );
}

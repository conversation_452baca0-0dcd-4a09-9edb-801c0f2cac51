import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, MessageCircle, Users, Coffee, Check } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep2Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

type PrimaryGoal = "more_matches" | "better_conversations" | "serious_relationship" | "casual_dating";

export function WizardStep2({ onComplete, wizardData, isCompleted }: WizardStep2Props) {
  const [primaryGoal, setPrimaryGoal] = useState<PrimaryGoal | undefined>(wizardData.primaryGoal);
  const [secondaryGoals, setSecondaryGoals] = useState<string[]>(wizardData.secondaryGoals || []);

  const primaryGoals = [
    {
      id: "more_matches" as const,
      icon: Users,
      title: "Get More Matches",
      description: "Increase your match rate and attract more potential partners",
      color: "bg-blue-500",
    },
    {
      id: "better_conversations" as const,
      icon: MessageCircle,
      title: "Better Conversations",
      description: "Start more engaging conversations that lead somewhere",
      color: "bg-green-500",
    },
    {
      id: "serious_relationship" as const,
      icon: Heart,
      title: "Find a Serious Relationship",
      description: "Attract people looking for long-term commitment",
      color: "bg-red-500",
    },
    {
      id: "casual_dating" as const,
      icon: Coffee,
      title: "Casual Dating",
      description: "Meet people for fun, casual dates and experiences",
      color: "bg-orange-500",
    },
  ];

  const secondaryGoalOptions = [
    "Improve photo quality",
    "Write a better bio",
    "Learn what works on my platform",
    "Understand my target audience",
    "Build confidence",
    "Stand out from the crowd",
  ];

  useEffect(() => {
    if (primaryGoal && !isCompleted) {
      onComplete({ 
        primaryGoal, 
        secondaryGoals 
      });
    }
  }, [primaryGoal, secondaryGoals, isCompleted, onComplete]);

  const handlePrimaryGoalSelect = (goal: PrimaryGoal) => {
    console.log(`🎯 Selected primary goal: ${goal}`);
    setPrimaryGoal(goal);
  };

  const handleSecondaryGoalToggle = (goal: string) => {
    setSecondaryGoals(prev => {
      const updated = prev.includes(goal)
        ? prev.filter(g => g !== goal)
        : [...prev, goal];
      console.log(`🎯 Updated secondary goals:`, updated);
      return updated;
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          What's Your Main Dating Goal?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          This helps us personalize your profile optimization strategy
        </motion.p>
      </div>

      {/* Primary Goals */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <h3 className="font-semibold text-graphite-90">Primary Goal</h3>
        <div className="grid md:grid-cols-2 gap-4">
          {primaryGoals.map((goal, index) => (
            <motion.div
              key={goal.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Card
                className={cn(
                  "cursor-pointer transition-all duration-200 hover:shadow-md",
                  primaryGoal === goal.id
                    ? "border-flame-red bg-flame-red/5"
                    : "border-graphite-20 hover:border-graphite-30"
                )}
                onClick={() => handlePrimaryGoalSelect(goal.id)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={cn("p-2 rounded-lg", goal.color, "text-white")}>
                      <goal.icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-graphite-90 mb-1">
                        {goal.title}
                      </h4>
                      <p className="text-sm text-graphite-60">
                        {goal.description}
                      </p>
                    </div>
                    {primaryGoal === goal.id && (
                      <Check className="w-5 h-5 text-flame-red" />
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Secondary Goals */}
      {primaryGoal && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="space-y-4"
        >
          <h3 className="font-semibold text-graphite-90">
            Additional Areas to Improve (Optional)
          </h3>
          <p className="text-sm text-graphite-60">
            Select any additional areas you'd like to focus on
          </p>
          
          <div className="grid md:grid-cols-2 gap-3">
            {secondaryGoalOptions.map((goal, index) => (
              <motion.div
                key={goal}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ delay: 0.5 + index * 0.05 }}
              >
                <Button
                  variant={secondaryGoals.includes(goal) ? "default" : "outline"}
                  className={cn(
                    "w-full justify-start h-auto p-4 text-left",
                    secondaryGoals.includes(goal)
                      ? "bg-flame-red hover:bg-flame-red/90"
                      : "hover:border-flame-red/30"
                  )}
                  onClick={() => handleSecondaryGoalToggle(goal)}
                >
                  <div className="flex items-center gap-3">
                    {secondaryGoals.includes(goal) && (
                      <Check className="w-4 h-4" />
                    )}
                    <span className="text-sm">{goal}</span>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Summary */}
      {primaryGoal && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="bg-gradient-to-r from-flame-red/5 to-sparks-pink/5 rounded-lg p-6"
        >
          <h4 className="font-semibold text-graphite-90 mb-3">Your Goals Summary</h4>
          <div className="space-y-2 text-sm">
            <p className="text-graphite-70">
              <strong>Primary:</strong> {primaryGoals.find(g => g.id === primaryGoal)?.title}
            </p>
            {secondaryGoals.length > 0 && (
              <p className="text-graphite-70">
                <strong>Also focusing on:</strong> {secondaryGoals.join(", ")}
              </p>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
}

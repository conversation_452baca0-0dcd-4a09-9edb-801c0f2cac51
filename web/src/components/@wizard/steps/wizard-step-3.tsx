import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { User, MapPin, Briefcase, Calendar } from "lucide-react";
import { motion } from "framer-motion";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep3Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep3({ onComplete, wizardData, isCompleted }: WizardStep3Props) {
  const [name, setName] = useState(wizardData.name || "");
  const [age, setAge] = useState(wizardData.age?.toString() || "");
  const [location, setLocation] = useState(wizardData.location || "");
  const [occupation, setOccupation] = useState(wizardData.occupation || "");

  const isFormValid = name.trim() && age && parseInt(age) >= 18 && parseInt(age) <= 100;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        name: name.trim(),
        age: parseInt(age),
        location: location.trim(),
        occupation: occupation.trim(),
      });
    }
  }, [name, age, location, occupation, isFormValid, isCompleted, onComplete]);

  const handleAgeChange = (value: string) => {
    // Only allow numbers
    const numericValue = value.replace(/\D/g, "");
    if (numericValue === "" || (parseInt(numericValue) >= 18 && parseInt(numericValue) <= 100)) {
      setAge(numericValue);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Tell Us About Yourself
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          This information helps us provide more personalized recommendations
        </motion.p>
      </div>

      {/* Form */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-8 space-y-6">
            {/* Name */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="space-y-2"
            >
              <Label htmlFor="name" className="flex items-center gap-2 text-graphite-90">
                <User className="w-4 h-4" />
                First Name *
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your first name"
                className="h-12"
                maxLength={50}
              />
              <p className="text-xs text-graphite-60">
                This helps us personalize your experience
              </p>
            </motion.div>

            {/* Age */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="space-y-2"
            >
              <Label htmlFor="age" className="flex items-center gap-2 text-graphite-90">
                <Calendar className="w-4 h-4" />
                Age *
              </Label>
              <Input
                id="age"
                value={age}
                onChange={(e) => handleAgeChange(e.target.value)}
                placeholder="Enter your age"
                className="h-12"
                maxLength={3}
              />
              <p className="text-xs text-graphite-60">
                Must be 18 or older to use TinderOP
              </p>
            </motion.div>

            {/* Location */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="space-y-2"
            >
              <Label htmlFor="location" className="flex items-center gap-2 text-graphite-90">
                <MapPin className="w-4 h-4" />
                Location (Optional)
              </Label>
              <Input
                id="location"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder="e.g., New York, NY"
                className="h-12"
                maxLength={100}
              />
              <p className="text-xs text-graphite-60">
                Helps us understand your dating market
              </p>
            </motion.div>

            {/* Occupation */}
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="space-y-2"
            >
              <Label htmlFor="occupation" className="flex items-center gap-2 text-graphite-90">
                <Briefcase className="w-4 h-4" />
                Occupation (Optional)
              </Label>
              <Input
                id="occupation"
                value={occupation}
                onChange={(e) => setOccupation(e.target.value)}
                placeholder="e.g., Software Engineer, Teacher, Student"
                className="h-12"
                maxLength={100}
              />
              <p className="text-xs text-graphite-60">
                Helps us tailor bio suggestions to your profession
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Privacy Notice */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6"
      >
        <h4 className="font-semibold text-graphite-90 mb-3 flex items-center gap-2">
          🔒 Privacy & Security
        </h4>
        <div className="space-y-2 text-sm text-graphite-70">
          <p>• Your personal information is stored locally on your device</p>
          <p>• We never share your data with third parties</p>
          <p>• You can clear all data at any time from your account settings</p>
          <p>• This information is only used to improve your recommendations</p>
        </div>
      </motion.div>

      {/* Validation Status */}
      {!isFormValid && name && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <p className="text-sm text-graphite-60">
            {!name.trim() && "Please enter your name"}
            {name.trim() && !age && "Please enter your age"}
            {name.trim() && age && (parseInt(age) < 18 || parseInt(age) > 100) && 
              "Age must be between 18 and 100"}
          </p>
        </motion.div>
      )}

      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            ✓ Profile information complete
          </div>
        </motion.div>
      )}
    </div>
  );
}

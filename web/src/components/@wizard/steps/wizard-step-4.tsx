import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Heart, Users, Coffee, Check } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep4Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep4({ onComplete, wizardData, isCompleted }: WizardStep4Props) {
  const [targetAgeRange, setTargetAgeRange] = useState(
    wizardData.targetAgeRange || { min: 22, max: 35 }
  );
  const [targetGender, setTargetGender] = useState<"men" | "women" | "everyone" | undefined>(
    wizardData.targetGender
  );
  const [relationshipType, setRelationshipType] = useState<"casual" | "serious" | "open" | undefined>(
    wizardData.relationshipType
  );

  const isFormValid = targetGender && relationshipType;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        targetAgeRange,
        targetGender,
        relationshipType,
      });
    }
  }, [targetAgeRange, targetGender, relationshipType, isFormValid, isCompleted, onComplete]);

  const genderOptions = [
    { id: "men" as const, label: "Men", icon: "👨" },
    { id: "women" as const, label: "Women", icon: "👩" },
    { id: "everyone" as const, label: "Everyone", icon: "🌈" },
  ];

  const relationshipOptions = [
    {
      id: "casual" as const,
      title: "Casual Dating",
      description: "Fun dates, no pressure",
      icon: Coffee,
      color: "bg-orange-500",
    },
    {
      id: "serious" as const,
      title: "Serious Relationship",
      description: "Looking for something long-term",
      icon: Heart,
      color: "bg-red-500",
    },
    {
      id: "open" as const,
      title: "Open to Both",
      description: "See where things go naturally",
      icon: Users,
      color: "bg-purple-500",
    },
  ];

  const handleAgeRangeChange = (values: number[]) => {
    setTargetAgeRange({ min: values[0], max: values[1] });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Dating Preferences
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          Help us understand who you're looking to meet and what you're seeking
        </motion.p>
      </div>

      {/* Age Range */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-6 space-y-6">
            <div className="space-y-4">
              <Label className="text-lg font-semibold text-graphite-90">
                Age Range You're Interested In
              </Label>
              <div className="space-y-4">
                <div className="px-4">
                  <Slider
                    value={[targetAgeRange.min, targetAgeRange.max]}
                    onValueChange={handleAgeRangeChange}
                    min={18}
                    max={65}
                    step={1}
                    className="w-full"
                  />
                </div>
                <div className="flex justify-between text-sm text-graphite-60">
                  <span>18</span>
                  <span className="font-semibold text-graphite-90">
                    {targetAgeRange.min} - {targetAgeRange.max} years old
                  </span>
                  <span>65+</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Gender Preference */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="space-y-4"
      >
        <Label className="text-lg font-semibold text-graphite-90">
          Who are you interested in meeting?
        </Label>
        <div className="grid grid-cols-3 gap-4">
          {genderOptions.map((option, index) => (
            <motion.div
              key={option.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <Button
                variant={targetGender === option.id ? "default" : "outline"}
                className={cn(
                  "w-full h-20 flex-col gap-2",
                  targetGender === option.id
                    ? "bg-flame-red hover:bg-flame-red/90"
                    : "hover:border-flame-red/30"
                )}
                onClick={() => setTargetGender(option.id)}
              >
                <span className="text-2xl">{option.icon}</span>
                <span className="text-sm">{option.label}</span>
                {targetGender === option.id && (
                  <Check className="w-4 h-4 absolute top-2 right-2" />
                )}
              </Button>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Relationship Type */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5 }}
        className="space-y-4"
      >
        <Label className="text-lg font-semibold text-graphite-90">
          What type of relationship are you seeking?
        </Label>
        <div className="space-y-3">
          {relationshipOptions.map((option, index) => (
            <motion.div
              key={option.id}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.6 + index * 0.1 }}
            >
              <Card
                className={cn(
                  "cursor-pointer transition-all duration-200 hover:shadow-md",
                  relationshipType === option.id
                    ? "border-flame-red bg-flame-red/5"
                    : "border-graphite-20 hover:border-graphite-30"
                )}
                onClick={() => setRelationshipType(option.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <div className={cn("p-2 rounded-lg", option.color, "text-white")}>
                      <option.icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-graphite-90">
                        {option.title}
                      </h4>
                      <p className="text-sm text-graphite-60">
                        {option.description}
                      </p>
                    </div>
                    {relationshipType === option.id && (
                      <Check className="w-5 h-5 text-flame-red" />
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Summary */}
      {isFormValid && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="bg-gradient-to-r from-flame-red/5 to-sparks-pink/5 rounded-lg p-6"
        >
          <h4 className="font-semibold text-graphite-90 mb-3">Your Preferences</h4>
          <div className="space-y-2 text-sm text-graphite-70">
            <p>
              <strong>Looking for:</strong> {genderOptions.find(g => g.id === targetGender)?.label}
            </p>
            <p>
              <strong>Age range:</strong> {targetAgeRange.min} - {targetAgeRange.max} years old
            </p>
            <p>
              <strong>Relationship type:</strong> {relationshipOptions.find(r => r.id === relationshipType)?.title}
            </p>
          </div>
        </motion.div>
      )}

      {/* Validation Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            ✓ Preferences set successfully
          </div>
        </motion.div>
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Heart, Zap, MessageSquare, Plus, X, Check } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep5Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep5({ onComplete, wizardData, isCompleted }: WizardStep5Props) {
  const [primaryPlatform, setPrimaryPlatform] = useState<"tinder" | "bumble" | "hinge" | "other" | undefined>(
    wizardData.primaryPlatform
  );
  const [otherPlatforms, setOtherPlatforms] = useState<string[]>(wizardData.otherPlatforms || []);
  const [customPlatform, setCustomPlatform] = useState("");

  const isFormValid = primaryPlatform !== undefined;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        primaryPlatform,
        otherPlatforms,
      });
    }
  }, [primaryPlatform, otherPlatforms, isFormValid, isCompleted, onComplete]);

  const platforms = [
    {
      id: "tinder" as const,
      name: "Tinder",
      icon: "🔥",
      description: "Swipe-based, casual to serious",
      color: "bg-red-500",
      tips: "Focus on eye-catching photos and short, witty bios",
    },
    {
      id: "bumble" as const,
      name: "Bumble",
      icon: "🐝",
      description: "Women message first",
      color: "bg-yellow-500",
      tips: "Professional photos work well, show your personality",
    },
    {
      id: "hinge" as const,
      name: "Hinge",
      icon: "💝",
      description: "Designed to be deleted",
      color: "bg-purple-500",
      tips: "Detailed prompts and authentic photos perform best",
    },
    {
      id: "other" as const,
      name: "Other",
      icon: "📱",
      description: "Different platform",
      color: "bg-gray-500",
      tips: "We'll provide general optimization advice",
    },
  ];

  const additionalPlatformOptions = [
    "Coffee Meets Bagel",
    "OkCupid",
    "Match.com",
    "eHarmony",
    "Plenty of Fish",
    "Facebook Dating",
  ];

  const handleAddOtherPlatform = (platform: string) => {
    if (!otherPlatforms.includes(platform)) {
      setOtherPlatforms([...otherPlatforms, platform]);
    }
  };

  const handleRemoveOtherPlatform = (platform: string) => {
    setOtherPlatforms(otherPlatforms.filter(p => p !== platform));
  };

  const handleAddCustomPlatform = () => {
    if (customPlatform.trim() && !otherPlatforms.includes(customPlatform.trim())) {
      setOtherPlatforms([...otherPlatforms, customPlatform.trim()]);
      setCustomPlatform("");
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Which Dating Platform Do You Use?
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          Different platforms have different best practices. We'll optimize for your main platform.
        </motion.p>
      </div>

      {/* Primary Platform Selection */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <Label className="text-lg font-semibold text-graphite-90">
          Primary Platform *
        </Label>
        <div className="grid md:grid-cols-2 gap-4">
          {platforms.map((platform, index) => (
            <motion.div
              key={platform.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Card
                className={cn(
                  "cursor-pointer transition-all duration-200 hover:shadow-md",
                  primaryPlatform === platform.id
                    ? "border-flame-red bg-flame-red/5"
                    : "border-graphite-20 hover:border-graphite-30"
                )}
                onClick={() => setPrimaryPlatform(platform.id)}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-4">
                      <div className={cn("p-2 rounded-lg", platform.color, "text-white text-xl")}>
                        {platform.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-graphite-90 mb-1">
                          {platform.name}
                        </h4>
                        <p className="text-sm text-graphite-60 mb-2">
                          {platform.description}
                        </p>
                      </div>
                      {primaryPlatform === platform.id && (
                        <Check className="w-5 h-5 text-flame-red" />
                      )}
                    </div>
                    
                    {primaryPlatform === platform.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        className="pt-3 border-t border-graphite-20"
                      >
                        <p className="text-xs text-graphite-60">
                          <strong>Optimization tip:</strong> {platform.tips}
                        </p>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Additional Platforms */}
      {primaryPlatform && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="space-y-4"
        >
          <Label className="text-lg font-semibold text-graphite-90">
            Other Platforms You Use (Optional)
          </Label>
          <p className="text-sm text-graphite-60">
            We can provide additional tips for these platforms too
          </p>

          {/* Quick Add Buttons */}
          <div className="flex flex-wrap gap-2">
            {additionalPlatformOptions.map((platform) => (
              <Button
                key={platform}
                variant={otherPlatforms.includes(platform) ? "default" : "outline"}
                size="sm"
                onClick={() => 
                  otherPlatforms.includes(platform) 
                    ? handleRemoveOtherPlatform(platform)
                    : handleAddOtherPlatform(platform)
                }
                className={cn(
                  "text-xs",
                  otherPlatforms.includes(platform)
                    ? "bg-flame-red hover:bg-flame-red/90"
                    : "hover:border-flame-red/30"
                )}
              >
                {otherPlatforms.includes(platform) && <Check className="w-3 h-3 mr-1" />}
                {platform}
              </Button>
            ))}
          </div>

          {/* Custom Platform Input */}
          <div className="flex gap-2">
            <Input
              value={customPlatform}
              onChange={(e) => setCustomPlatform(e.target.value)}
              placeholder="Add another platform..."
              className="flex-1"
              onKeyPress={(e) => e.key === "Enter" && handleAddCustomPlatform()}
            />
            <Button
              onClick={handleAddCustomPlatform}
              disabled={!customPlatform.trim()}
              size="icon"
              variant="outline"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>

          {/* Selected Additional Platforms */}
          {otherPlatforms.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-graphite-90">Selected platforms:</p>
              <div className="flex flex-wrap gap-2">
                {otherPlatforms.map((platform) => (
                  <div
                    key={platform}
                    className="flex items-center gap-2 px-3 py-1 bg-flame-red/10 text-flame-red rounded-full text-sm"
                  >
                    {platform}
                    <button
                      onClick={() => handleRemoveOtherPlatform(platform)}
                      className="hover:bg-flame-red/20 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}

      {/* Platform-Specific Tips */}
      {primaryPlatform && primaryPlatform !== "other" && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6"
        >
          <h4 className="font-semibold text-graphite-90 mb-3">
            {platforms.find(p => p.id === primaryPlatform)?.name} Optimization Tips
          </h4>
          <div className="space-y-2 text-sm text-graphite-70">
            {primaryPlatform === "tinder" && (
              <>
                <p>• First photo is crucial - make it count</p>
                <p>• Keep bio short and engaging (under 500 characters)</p>
                <p>• Show variety in your photo selection</p>
                <p>• Avoid group photos as your main picture</p>
              </>
            )}
            {primaryPlatform === "bumble" && (
              <>
                <p>• Professional-looking photos perform well</p>
                <p>• Fill out all profile sections</p>
                <p>• Use the prompt features effectively</p>
                <p>• Show your interests and hobbies clearly</p>
              </>
            )}
            {primaryPlatform === "hinge" && (
              <>
                <p>• Answer prompts thoughtfully and authentically</p>
                <p>• Use all 6 photo slots</p>
                <p>• Mix photos with prompt responses</p>
                <p>• Show your personality through captions</p>
              </>
            )}
          </div>
        </motion.div>
      )}

      {/* Validation Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            ✓ Platform preferences saved
          </div>
        </motion.div>
      )}
    </div>
  );
}

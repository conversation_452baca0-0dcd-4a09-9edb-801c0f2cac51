import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Camera, FileText, Lightbulb, Shield, CheckCircle, AlertTriangle } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import type { WizardData } from "@/stores/wizardStore";

interface WizardStep6Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep6({ onComplete, wizardData, isCompleted }: WizardStep6Props) {
  const [hasReadTips, setHasReadTips] = useState(wizardData.hasReadTips || false);
  const [isReadyForAnalysis, setIsReadyForAnalysis] = useState(wizardData.isReadyForAnalysis || false);
  const [checkedTips, setCheckedTips] = useState<Set<number>>(new Set());

  const isFormValid = hasReadTips && isReadyForAnalysis;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        hasReadTips,
        isReadyForAnalysis,
      });
    }
  }, [hasReadTips, isReadyForAnalysis, isFormValid, isCompleted, onComplete]);

  const preparationTips = [
    {
      icon: Camera,
      title: "Photo Preparation",
      items: [
        "Have 3-6 high-quality photos ready",
        "Include at least one clear face shot",
        "Show variety: close-up, full body, activity shots",
        "Ensure good lighting in all photos",
        "Avoid heavily filtered or edited images",
      ],
    },
    {
      icon: FileText,
      title: "Bio Preparation",
      items: [
        "Think about your current bio (if you have one)",
        "Consider your hobbies and interests",
        "Think about what makes you unique",
        "Consider conversation starters",
        "Be ready to share what you're looking for",
      ],
    },
    {
      icon: Shield,
      title: "Privacy & Safety",
      items: [
        "Your photos are processed locally and securely",
        "We never store your images on our servers",
        "All analysis happens in real-time",
        "You can delete your data at any time",
        "No personal information is shared with third parties",
      ],
    },
  ];

  const handleTipCheck = (tipIndex: number, itemIndex: number) => {
    const key = tipIndex * 10 + itemIndex;
    const newChecked = new Set(checkedTips);
    
    if (newChecked.has(key)) {
      newChecked.delete(key);
    } else {
      newChecked.add(key);
    }
    
    setCheckedTips(newChecked);
    
    // Check if user has interacted with tips
    if (newChecked.size >= 5 && !hasReadTips) {
      setHasReadTips(true);
    }
  };

  const handleReadyToggle = () => {
    setIsReadyForAnalysis(!isReadyForAnalysis);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Let's Prepare for Analysis
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          Review these tips to get the most out of your profile optimization
        </motion.p>
      </div>

      {/* Preparation Tips */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        {preparationTips.map((tip, tipIndex) => (
          <motion.div
            key={tipIndex}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3 + tipIndex * 0.1 }}
          >
            <Card className="border border-graphite-20">
              <CardContent className="p-6">
                <div className="flex items-start gap-4 mb-4">
                  <div className="p-2 rounded-lg bg-flame-red/10">
                    <tip.icon className="w-5 h-5 text-flame-red" />
                  </div>
                  <h3 className="font-semibold text-graphite-90 text-lg">
                    {tip.title}
                  </h3>
                </div>
                
                <div className="space-y-3 ml-13">
                  {tip.items.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 + tipIndex * 0.1 + itemIndex * 0.05 }}
                      className="flex items-start gap-3"
                    >
                      <Checkbox
                        id={`tip-${tipIndex}-${itemIndex}`}
                        checked={checkedTips.has(tipIndex * 10 + itemIndex)}
                        onCheckedChange={() => handleTipCheck(tipIndex, itemIndex)}
                        className="mt-0.5"
                      />
                      <label
                        htmlFor={`tip-${tipIndex}-${itemIndex}`}
                        className={cn(
                          "text-sm cursor-pointer transition-colors",
                          checkedTips.has(tipIndex * 10 + itemIndex)
                            ? "text-graphite-90 line-through"
                            : "text-graphite-70"
                        )}
                      >
                        {item}
                      </label>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* What Happens Next */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.6 }}
        className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6"
      >
        <h4 className="font-semibold text-graphite-90 mb-4 flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-blue-600" />
          What Happens Next?
        </h4>
        <div className="grid md:grid-cols-2 gap-4 text-sm text-graphite-70">
          <div className="space-y-2">
            <p className="font-medium text-graphite-90">Step 7: Photo Analysis</p>
            <p>• Upload your dating photos</p>
            <p>• Get AI-powered feedback on each image</p>
            <p>• Receive specific improvement suggestions</p>
          </div>
          <div className="space-y-2">
            <p className="font-medium text-graphite-90">Step 8: Bio Optimization</p>
            <p>• Analyze your current bio (or create one)</p>
            <p>• Get personalized writing suggestions</p>
            <p>• Generate improved bio variations</p>
          </div>
        </div>
      </motion.div>

      {/* Ready Confirmation */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7 }}
        className="space-y-4"
      >
        <Card className={cn(
          "border-2 transition-colors",
          isReadyForAnalysis ? "border-green-300 bg-green-50" : "border-graphite-20"
        )}>
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Checkbox
                id="ready-confirmation"
                checked={isReadyForAnalysis}
                onCheckedChange={handleReadyToggle}
                className="mt-1"
              />
              <div className="flex-1">
                <label
                  htmlFor="ready-confirmation"
                  className="font-semibold text-graphite-90 cursor-pointer block mb-2"
                >
                  I'm ready to start analyzing my profile
                </label>
                <p className="text-sm text-graphite-60">
                  I understand the process and have my photos and bio information ready for analysis.
                </p>
              </div>
              {isReadyForAnalysis && (
                <CheckCircle className="w-6 h-6 text-green-600" />
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Progress Indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
        className="text-center"
      >
        {!hasReadTips && (
          <div className="flex items-center justify-center gap-2 text-sm text-graphite-60">
            <AlertTriangle className="w-4 h-4" />
            Check off some preparation items to continue
          </div>
        )}
        
        {hasReadTips && !isReadyForAnalysis && (
          <div className="flex items-center justify-center gap-2 text-sm text-graphite-60">
            <CheckCircle className="w-4 h-4 text-green-600" />
            Great! Now confirm you're ready to proceed
          </div>
        )}
        
        {isFormValid && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm"
          >
            <CheckCircle className="w-4 h-4" />
            Ready to start profile analysis!
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}

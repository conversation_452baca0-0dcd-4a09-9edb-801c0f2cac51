import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useDropzone } from "react-dropzone";
import { Camera, Upload, X, Loader2, CheckCircle, AlertCircle, Eye } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { analysisService } from "@/lib/analysis-service";
import { createImagePreview, revokeImagePreview } from "@/lib/storage";
import type { WizardData } from "@/stores/wizardStore";
import type { AnalysisResult, AnalysisProgress } from "@/types/analysis";

interface WizardStep7Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

interface UploadedPhoto {
  id: string;
  fileName: string;
  preview: string;
  file: File;
  analysisResult?: AnalysisResult;
  isAnalyzing?: boolean;
  analysisProgress?: number;
}

export function WizardStep7({ onComplete, wizardData, isCompleted }: WizardStep7Props) {
  const [photos, setPhotos] = useState<UploadedPhoto[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState<{ [key: string]: AnalysisProgress }>({});
  const [showResults, setShowResults] = useState(false);

  const hasPhotos = photos.length > 0;
  const allPhotosAnalyzed = photos.length > 0 && photos.every(photo => photo.analysisResult);
  const isFormValid = allPhotosAnalyzed;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      const photoData = photos.map(photo => ({
        id: photo.id,
        fileName: photo.fileName,
        preview: photo.preview,
        analysisResult: photo.analysisResult,
      }));

      onComplete({
        uploadedPhotos: photoData,
        photoAnalysisCompleted: true,
      });
    }
  }, [photos, isFormValid, isCompleted, onComplete]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    console.log("📸 Wizard Step 7: Files dropped", acceptedFiles.length);
    
    const newPhotos: UploadedPhoto[] = [];
    
    for (const file of acceptedFiles) {
      if (photos.length + newPhotos.length >= 6) break; // Limit to 6 photos
      
      const preview = await createImagePreview(file);
      const photo: UploadedPhoto = {
        id: `wizard-photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        fileName: file.name,
        preview,
        file,
      };
      
      newPhotos.push(photo);
    }
    
    setPhotos(prev => [...prev, ...newPhotos]);
    
    // Auto-start analysis for new photos
    if (newPhotos.length > 0) {
      analyzePhotos([...photos, ...newPhotos]);
    }
  }, [photos]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { "image/*": [] },
    maxFiles: 6 - photos.length,
    disabled: photos.length >= 6 || isAnalyzing,
  });

  const analyzePhotos = async (photosToAnalyze: UploadedPhoto[]) => {
    setIsAnalyzing(true);
    console.log("🔍 Wizard Step 7: Starting analysis for", photosToAnalyze.length, "photos");

    for (const photo of photosToAnalyze) {
      if (photo.analysisResult) continue; // Skip already analyzed photos

      try {
        setPhotos(prev => prev.map(p => 
          p.id === photo.id ? { ...p, isAnalyzing: true } : p
        ));

        const result = await analysisService.analyzeImage(
          photo.file,
          (progress: AnalysisProgress) => {
            console.log(`📊 Analysis progress for ${photo.fileName}:`, progress);
            setAnalysisProgress(prev => ({
              ...prev,
              [photo.id]: progress,
            }));
          }
        );

        console.log(`✅ Analysis completed for ${photo.fileName}:`, result);

        setPhotos(prev => prev.map(p => 
          p.id === photo.id 
            ? { ...p, analysisResult: result, isAnalyzing: false }
            : p
        ));

        // Clean up progress tracking
        setAnalysisProgress(prev => {
          const updated = { ...prev };
          delete updated[photo.id];
          return updated;
        });

      } catch (error) {
        console.error(`❌ Analysis failed for ${photo.fileName}:`, error);
        setPhotos(prev => prev.map(p => 
          p.id === photo.id ? { ...p, isAnalyzing: false } : p
        ));
      }
    }

    setIsAnalyzing(false);
    setShowResults(true);
  };

  const removePhoto = (photoId: string) => {
    setPhotos(prev => {
      const photoToRemove = prev.find(p => p.id === photoId);
      if (photoToRemove) {
        revokeImagePreview(photoToRemove.preview);
      }
      return prev.filter(p => p.id !== photoId);
    });
  };

  const getOverallScore = () => {
    if (!allPhotosAnalyzed) return 0;
    const scores = photos.map(photo => photo.analysisResult?.overallScore || 0);
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Upload Your Photos
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          Upload 3-6 photos for AI analysis. We'll provide detailed feedback on each one.
        </motion.p>
      </div>

      {/* Upload Area */}
      {photos.length < 6 && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
              isDragActive
                ? "border-flame-red bg-flame-red/5"
                : "border-graphite-30 hover:border-flame-red/50 hover:bg-flame-red/5",
              isAnalyzing && "pointer-events-none opacity-50"
            )}
          >
            <input {...getInputProps()} />
            <Camera className="w-12 h-12 text-graphite-40 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-graphite-90 mb-2">
              {isDragActive ? "Drop your photos here" : "Upload Your Dating Photos"}
            </h3>
            <p className="text-graphite-60 mb-4">
              Drag & drop photos here, or click to select files
            </p>
            <Button disabled={isAnalyzing}>
              <Upload className="w-4 h-4 mr-2" />
              Choose Photos
            </Button>
            <p className="text-xs text-graphite-50 mt-3">
              Supports JPG, PNG • Max 6 photos • {6 - photos.length} remaining
            </p>
          </div>
        </motion.div>
      )}

      {/* Photo Grid */}
      {hasPhotos && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="space-y-4"
        >
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-graphite-90">
              Your Photos ({photos.length}/6)
            </h3>
            {allPhotosAnalyzed && (
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Analysis Complete
              </Badge>
            )}
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {photos.map((photo, index) => (
                <motion.div
                  key={photo.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="overflow-hidden">
                    <div className="relative">
                      <img
                        src={photo.preview}
                        alt={photo.fileName}
                        className="w-full h-48 object-cover"
                      />
                      
                      {/* Remove Button */}
                      <button
                        onClick={() => removePhoto(photo.id)}
                        className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                        disabled={isAnalyzing}
                      >
                        <X className="w-4 h-4" />
                      </button>

                      {/* Analysis Status Overlay */}
                      {photo.isAnalyzing && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                          <div className="text-center text-white">
                            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                            <p className="text-sm">Analyzing...</p>
                            {analysisProgress[photo.id] && (
                              <div className="mt-2 w-24 mx-auto">
                                <Progress 
                                  value={analysisProgress[photo.id].progress} 
                                  className="h-1"
                                />
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    <CardContent className="p-4">
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-graphite-90 truncate">
                          {photo.fileName}
                        </p>
                        
                        {photo.analysisResult && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-graphite-60">Overall Score</span>
                              <Badge 
                                variant={photo.analysisResult.overallScore >= 70 ? "default" : "secondary"}
                                className={cn(
                                  photo.analysisResult.overallScore >= 70 
                                    ? "bg-green-100 text-green-800" 
                                    : "bg-orange-100 text-orange-800"
                                )}
                              >
                                {photo.analysisResult.overallScore}/100
                              </Badge>
                            </div>
                            
                            {showResults && (
                              <div className="text-xs text-graphite-60">
                                <p className="truncate">
                                  {photo.analysisResult.recommendations[0] || "Analysis complete"}
                                </p>
                              </div>
                            )}
                          </div>
                        )}

                        {!photo.analysisResult && !photo.isAnalyzing && (
                          <div className="flex items-center gap-2 text-sm text-graphite-60">
                            <AlertCircle className="w-4 h-4" />
                            Waiting for analysis
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </motion.div>
      )}

      {/* Results Summary */}
      {allPhotosAnalyzed && showResults && (
        <motion.div
          initial={{ y: 40, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="bg-gradient-to-r from-flame-red/5 to-sparks-pink/5 rounded-lg p-6"
        >
          <h4 className="font-semibold text-graphite-90 mb-4 flex items-center gap-2">
            <Eye className="w-5 h-5 text-flame-red" />
            Analysis Summary
          </h4>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm text-graphite-60 mb-2">Average Score</p>
              <div className="text-3xl font-bold text-graphite-90">
                {getOverallScore()}/100
              </div>
              <p className="text-sm text-graphite-60 mt-1">
                {getOverallScore() >= 70 ? "Great photos!" : "Room for improvement"}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-graphite-60 mb-2">Next Steps</p>
              <div className="space-y-1 text-sm text-graphite-70">
                <p>✓ Photos analyzed successfully</p>
                <p>✓ Detailed feedback available</p>
                <p>→ Continue to bio optimization</p>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Validation Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            Photo analysis complete! Ready for bio optimization.
          </div>
        </motion.div>
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { FileText, Loader2, CheckCircle, Lightbulb, Copy, RefreshCw, Sparkles } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { analysisService } from "@/lib/analysis-service";
import type { WizardData } from "@/stores/wizardStore";
import type { BioAnalysisResult } from "@/types/analysis";

interface WizardStep8Props {
  onComplete: (data?: Partial<WizardData>) => void;
  wizardData: WizardData;
  isCompleted: boolean;
}

export function WizardStep8({ onComplete, wizardData, isCompleted }: WizardStep8Props) {
  const [originalBio, setOriginalBio] = useState(wizardData.originalBio || "");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<BioAnalysisResult | null>(
    wizardData.bioAnalysisResult || null
  );
  const [improvedBio, setImprovedBio] = useState(wizardData.improvedBio || "");
  const [isGeneratingImprovement, setIsGeneratingImprovement] = useState(false);
  const [selectedTone, setSelectedTone] = useState<"witty" | "sincere" | "adventurous">("sincere");
  const [analysisProgress, setAnalysisProgress] = useState(0);

  const hasOriginalBio = originalBio.trim().length > 0;
  const hasAnalysis = analysisResult !== null;
  const hasImprovedBio = improvedBio.trim().length > 0;
  const isFormValid = hasOriginalBio && hasAnalysis;

  useEffect(() => {
    if (isFormValid && !isCompleted) {
      onComplete({
        originalBio: originalBio.trim(),
        bioAnalysisResult: analysisResult,
        improvedBio: improvedBio.trim(),
        bioAnalysisCompleted: true,
      });
    }
  }, [originalBio, analysisResult, improvedBio, isFormValid, isCompleted, onComplete]);

  const toneOptions = [
    {
      id: "sincere" as const,
      label: "Sincere",
      description: "Genuine and authentic",
      emoji: "💝",
    },
    {
      id: "witty" as const,
      label: "Witty",
      description: "Clever and humorous",
      emoji: "😄",
    },
    {
      id: "adventurous" as const,
      label: "Adventurous",
      description: "Bold and exciting",
      emoji: "🌟",
    },
  ];

  const analyzeBio = async () => {
    if (!hasOriginalBio) return;

    setIsAnalyzing(true);
    setAnalysisProgress(0);
    console.log("🔍 Wizard Step 8: Starting bio analysis");

    try {
      const result = await analysisService.analyzeBio(
        originalBio.trim(),
        "sincere",
        {
          onProgress: (progress) => {
            console.log(`📊 Bio analysis progress: Step ${progress.currentStep} (${progress.stepName}) - ${progress.progress}%`);
            setAnalysisProgress(progress.progress);
          },
          onComplete: (analysisResult) => {
            console.log("✅ Bio analysis completed:", analysisResult);
            setAnalysisResult(analysisResult);
          },
          onError: (error) => {
            console.error("❌ Bio analysis failed:", error);
            throw new Error(error);
          }
        }
      );
    } catch (error) {
      console.error("❌ Bio analysis failed:", error);
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  };

  const generateImprovedBio = async () => {
    if (!analysisResult) return;

    setIsGeneratingImprovement(true);
    console.log(`🚀 Generating improved bio with ${selectedTone} tone`);

    try {
      // Use the bio analysis agent directly for improvement generation
      const { BioAnalysisAgent } = await import("@/lib/bio-analysis");
      const bioAgent = new BioAnalysisAgent();
      const improved = await bioAgent.generateImprovedBio(
        originalBio.trim(),
        analysisResult.steps,
        selectedTone
      );

      console.log("✅ Improved bio generated:", improved);
      setImprovedBio(improved);
    } catch (error) {
      console.error("❌ Bio improvement generation failed:", error);
    } finally {
      setIsGeneratingImprovement(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Could add a toast notification here
  };

  const getScoreColor = (score: number) => {
    if (score >= 70) return "bg-green-100 text-green-800";
    if (score >= 50) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const getOverallScore = () => {
    if (!analysisResult) return 0;
    return Math.round(
      analysisResult.steps.reduce((sum, step) => sum + step.score, 0) / analysisResult.steps.length
    );
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="text-2xl font-bold text-graphite-90"
        >
          Optimize Your Bio
        </motion.h2>
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="text-graphite-60"
        >
          Let's analyze your current bio and create an improved version that gets more matches
        </motion.p>
      </div>

      {/* Original Bio Input */}
      <motion.div
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        <Card className="border border-graphite-20">
          <CardContent className="p-6 space-y-4">
            <Label htmlFor="original-bio" className="flex items-center gap-2 text-lg font-semibold">
              <FileText className="w-5 h-5" />
              Your Current Bio
            </Label>
            
            <Textarea
              id="original-bio"
              value={originalBio}
              onChange={(e) => setOriginalBio(e.target.value)}
              placeholder="Paste your current dating bio here, or write a new one if you don't have one yet..."
              className="min-h-32 resize-none"
              maxLength={500}
              disabled={isAnalyzing}
            />
            
            <div className="flex justify-between items-center text-sm text-graphite-60">
              <span>{originalBio.length}/500 characters</span>
              {hasOriginalBio && !hasAnalysis && (
                <Button
                  onClick={analyzeBio}
                  disabled={isAnalyzing}
                  size="sm"
                >
                  {isAnalyzing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Analyze Bio
                    </>
                  )}
                </Button>
              )}
            </div>

            {/* Analysis Progress */}
            {isAnalyzing && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="space-y-2"
              >
                <Progress value={analysisProgress} className="h-2" />
                <p className="text-sm text-graphite-60 text-center">
                  Analyzing your bio with AI...
                </p>
              </motion.div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Analysis Results */}
      <AnimatePresence>
        {hasAnalysis && (
          <motion.div
            initial={{ y: 40, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-4"
          >
            <Card className="border border-graphite-20">
              <CardContent className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-graphite-90">Analysis Results</h3>
                  <Badge className={cn("text-sm", getScoreColor(getOverallScore()))}>
                    Overall Score: {getOverallScore()}/100
                  </Badge>
                </div>

                {/* Score Breakdown */}
                <div className="grid md:grid-cols-2 gap-4">
                  {analysisResult?.steps.map((step, index) => (
                    <motion.div
                      key={step.stepId}
                      initial={{ x: -20, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.4 + index * 0.1 }}
                      className="space-y-2"
                    >
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-graphite-90">
                          {step.stepName}
                        </span>
                        <Badge className={cn("text-xs", getScoreColor(step.score))}>
                          {step.score}/100
                        </Badge>
                      </div>
                      
                      {step.insights.length > 0 && (
                        <p className="text-xs text-graphite-60">
                          {step.insights[0]}
                        </p>
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Key Recommendations */}
                {analysisResult?.recommendations && analysisResult.recommendations.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-semibold text-graphite-90 flex items-center gap-2">
                      <Lightbulb className="w-4 h-4 text-yellow-600" />
                      Key Recommendations
                    </h4>
                    <div className="space-y-2">
                      {analysisResult.recommendations.slice(0, 3).map((rec, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.5 + index * 0.1 }}
                          className="flex items-start gap-2 text-sm text-graphite-70"
                        >
                          <span className="text-flame-red mt-1">•</span>
                          <span>{rec}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bio Improvement Section */}
      <AnimatePresence>
        {hasAnalysis && (
          <motion.div
            initial={{ y: 40, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <Card className="border border-graphite-20">
              <CardContent className="p-6 space-y-6">
                <h3 className="text-lg font-semibold text-graphite-90">Generate Improved Bio</h3>
                
                {/* Tone Selection */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Choose your tone:</Label>
                  <div className="grid grid-cols-3 gap-3">
                    {toneOptions.map((tone) => (
                      <Button
                        key={tone.id}
                        variant={selectedTone === tone.id ? "default" : "outline"}
                        className={cn(
                          "h-auto p-4 flex-col gap-2",
                          selectedTone === tone.id
                            ? "bg-flame-red hover:bg-flame-red/90"
                            : "hover:border-flame-red/30"
                        )}
                        onClick={() => setSelectedTone(tone.id)}
                        disabled={isGeneratingImprovement}
                      >
                        <span className="text-lg">{tone.emoji}</span>
                        <div className="text-center">
                          <div className="font-medium text-sm">{tone.label}</div>
                          <div className="text-xs opacity-80">{tone.description}</div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Generate Button */}
                <Button
                  onClick={generateImprovedBio}
                  disabled={isGeneratingImprovement}
                  className="w-full"
                  size="lg"
                >
                  {isGeneratingImprovement ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating improved bio...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Generate Improved Bio
                    </>
                  )}
                </Button>

                {/* Improved Bio Result */}
                <AnimatePresence>
                  {hasImprovedBio && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Your Improved Bio:</Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(improvedBio)}
                        >
                          <Copy className="w-3 h-3 mr-1" />
                          Copy
                        </Button>
                      </div>
                      
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-sm text-graphite-90 whitespace-pre-wrap">
                          {improvedBio}
                        </p>
                      </div>
                      
                      <p className="text-xs text-graphite-60">
                        This improved bio addresses the issues found in your analysis and uses a {selectedTone} tone.
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Completion Status */}
      {isFormValid && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-lg text-sm">
            <CheckCircle className="w-4 h-4" />
            Bio analysis complete! Your profile optimization is ready.
          </div>
        </motion.div>
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { useWizardStore } from "@/stores/wizardStore";

// Import wizard steps
import { WizardStep1 } from "./steps/wizard-step-1";
import { WizardStep2 } from "./steps/wizard-step-2";
import { WizardStep3 } from "./steps/wizard-step-3";
import { WizardStep4 } from "./steps/wizard-step-4";
import { WizardStep5 } from "./steps/wizard-step-5";
import { WizardStep6 } from "./steps/wizard-step-6";
import { WizardStep7 } from "./steps/wizard-step-7";
import { WizardStep8 } from "./steps/wizard-step-8";

const WIZARD_STEPS = [
  { id: 1, title: "Welcome", component: WizardStep1 },
  { id: 2, title: "Goals", component: WizardStep2 },
  { id: 3, title: "Profile Setup", component: WizardStep3 },
  { id: 4, title: "Preferences", component: WizardStep4 },
  { id: 5, title: "Dating Platform", component: WizardStep5 },
  { id: 6, title: "Preparation", component: WizardStep6 },
  { id: 7, title: "Photo Analysis", component: WizardStep7 },
  { id: 8, title: "Bio Optimization", component: WizardStep8 },
] as const;

interface WizardContainerProps {
  onComplete?: () => void;
}

export function WizardContainer({ onComplete }: WizardContainerProps) {
  const navigate = useNavigate();
  const {
    currentStep,
    setCurrentStep,
    wizardData,
    isStepCompleted,
    markStepCompleted,
    resetWizard,
  } = useWizardStore();

  const [isTransitioning, setIsTransitioning] = useState(false);

  const currentStepData = WIZARD_STEPS[currentStep - 1];
  const CurrentStepComponent = currentStepData.component;
  const progress = (currentStep / WIZARD_STEPS.length) * 100;

  const canGoNext = isStepCompleted(currentStep);
  const canGoPrevious = currentStep > 1;
  const isLastStep = currentStep === WIZARD_STEPS.length;

  const handleNext = async () => {
    if (!canGoNext) return;

    setIsTransitioning(true);
    
    if (isLastStep) {
      // Complete wizard
      console.log("🎉 Wizard completed with data:", wizardData);
      onComplete?.();
      navigate({ to: "/dashboard" });
    } else {
      // Go to next step
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        setIsTransitioning(false);
      }, 150);
    }
  };

  const handlePrevious = () => {
    if (!canGoPrevious) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setCurrentStep(currentStep - 1);
      setIsTransitioning(false);
    }, 150);
  };

  const handleStepComplete = (stepData?: any) => {
    console.log(`✅ Step ${currentStep} completed with data:`, stepData);
    markStepCompleted(currentStep, stepData);
  };

  // Reset wizard on mount if needed
  useEffect(() => {
    // Only reset if we're starting fresh
    if (currentStep === 1 && Object.keys(wizardData).length === 0) {
      console.log("🔄 Initializing fresh wizard session");
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
      <div className="w-full max-w-4xl">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-graphite-90">
              Setup Your Profile
            </h1>
            <div className="text-sm text-graphite-60">
              Step {currentStep} of {WIZARD_STEPS.length}
            </div>
          </div>
          
          <Progress value={progress} className="h-2" />
          
          <div className="flex justify-between mt-2 text-xs text-graphite-60">
            <span>Getting Started</span>
            <span>Profile Ready</span>
          </div>
        </div>

        {/* Step Content */}
        <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
          <CardContent className="p-8">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-graphite-90 mb-2">
                {currentStepData.title}
              </h2>
            </div>

            {/* Step Component */}
            <div
              className={cn(
                "transition-all duration-300",
                isTransitioning && "opacity-50 scale-95"
              )}
            >
              <CurrentStepComponent
                onComplete={handleStepComplete}
                wizardData={wizardData}
                isCompleted={isStepCompleted(currentStep)}
              />
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8 pt-6 border-t border-graphite-20">
              <Button
                variant="ghost"
                onClick={handlePrevious}
                disabled={!canGoPrevious || isTransitioning}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Previous
              </Button>

              <div className="flex items-center gap-2">
                {WIZARD_STEPS.map((step, index) => (
                  <div
                    key={step.id}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors",
                      index + 1 < currentStep
                        ? "bg-flame-red"
                        : index + 1 === currentStep
                        ? "bg-flame-red/60"
                        : "bg-graphite-20"
                    )}
                  />
                ))}
              </div>

              <Button
                onClick={handleNext}
                disabled={!canGoNext || isTransitioning}
                className="flex items-center gap-2"
              >
                {isLastStep ? "Complete Setup" : "Next"}
                {!isLastStep && <ArrowRight className="h-4 w-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

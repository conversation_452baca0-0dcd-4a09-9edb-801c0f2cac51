/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as WelcomeRouteImport } from './routes/welcome'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthedWizardRouteImport } from './routes/_authed/wizard'
import { Route as AuthedImageEditorRouteImport } from './routes/_authed/image-editor'
import { Route as AuthedImageAnalyzerProRouteImport } from './routes/_authed/image-analyzer-pro'
import { Route as AuthedImageAnalyzerRouteImport } from './routes/_authed/image-analyzer'
import { Route as AuthedDashboardRouteImport } from './routes/_authed/dashboard'
import { Route as AuthedBioAnalyzerProRouteImport } from './routes/_authed/bio-analyzer-pro'
import { Route as AuthedBioAnalyzerRouteImport } from './routes/_authed/bio-analyzer'
import { Route as AuthedAnalysisComparisonRouteImport } from './routes/_authed/analysis-comparison'
import { Route as AuthedAccountSettingsRouteImport } from './routes/_authed/account-settings'
import { Route as Authed404RouteImport } from './routes/_authed/404'

const WelcomeRoute = WelcomeRouteImport.update({
  id: '/welcome',
  path: '/welcome',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedWizardRoute = AuthedWizardRouteImport.update({
  id: '/_authed/wizard',
  path: '/wizard',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedImageEditorRoute = AuthedImageEditorRouteImport.update({
  id: '/_authed/image-editor',
  path: '/image-editor',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedImageAnalyzerProRoute = AuthedImageAnalyzerProRouteImport.update({
  id: '/_authed/image-analyzer-pro',
  path: '/image-analyzer-pro',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedImageAnalyzerRoute = AuthedImageAnalyzerRouteImport.update({
  id: '/_authed/image-analyzer',
  path: '/image-analyzer',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedDashboardRoute = AuthedDashboardRouteImport.update({
  id: '/_authed/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedBioAnalyzerProRoute = AuthedBioAnalyzerProRouteImport.update({
  id: '/_authed/bio-analyzer-pro',
  path: '/bio-analyzer-pro',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedBioAnalyzerRoute = AuthedBioAnalyzerRouteImport.update({
  id: '/_authed/bio-analyzer',
  path: '/bio-analyzer',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedAnalysisComparisonRoute =
  AuthedAnalysisComparisonRouteImport.update({
    id: '/_authed/analysis-comparison',
    path: '/analysis-comparison',
    getParentRoute: () => rootRouteImport,
  } as any)
const AuthedAccountSettingsRoute = AuthedAccountSettingsRouteImport.update({
  id: '/_authed/account-settings',
  path: '/account-settings',
  getParentRoute: () => rootRouteImport,
} as any)
const Authed404Route = Authed404RouteImport.update({
  id: '/_authed/404',
  path: '/404',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/welcome': typeof WelcomeRoute
  '/404': typeof Authed404Route
  '/account-settings': typeof AuthedAccountSettingsRoute
  '/analysis-comparison': typeof AuthedAnalysisComparisonRoute
  '/bio-analyzer': typeof AuthedBioAnalyzerRoute
  '/bio-analyzer-pro': typeof AuthedBioAnalyzerProRoute
  '/dashboard': typeof AuthedDashboardRoute
  '/image-analyzer': typeof AuthedImageAnalyzerRoute
  '/image-analyzer-pro': typeof AuthedImageAnalyzerProRoute
  '/image-editor': typeof AuthedImageEditorRoute
  '/wizard': typeof AuthedWizardRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/welcome': typeof WelcomeRoute
  '/404': typeof Authed404Route
  '/account-settings': typeof AuthedAccountSettingsRoute
  '/analysis-comparison': typeof AuthedAnalysisComparisonRoute
  '/bio-analyzer': typeof AuthedBioAnalyzerRoute
  '/bio-analyzer-pro': typeof AuthedBioAnalyzerProRoute
  '/dashboard': typeof AuthedDashboardRoute
  '/image-analyzer': typeof AuthedImageAnalyzerRoute
  '/image-analyzer-pro': typeof AuthedImageAnalyzerProRoute
  '/image-editor': typeof AuthedImageEditorRoute
  '/wizard': typeof AuthedWizardRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/welcome': typeof WelcomeRoute
  '/_authed/404': typeof Authed404Route
  '/_authed/account-settings': typeof AuthedAccountSettingsRoute
  '/_authed/analysis-comparison': typeof AuthedAnalysisComparisonRoute
  '/_authed/bio-analyzer': typeof AuthedBioAnalyzerRoute
  '/_authed/bio-analyzer-pro': typeof AuthedBioAnalyzerProRoute
  '/_authed/dashboard': typeof AuthedDashboardRoute
  '/_authed/image-analyzer': typeof AuthedImageAnalyzerRoute
  '/_authed/image-analyzer-pro': typeof AuthedImageAnalyzerProRoute
  '/_authed/image-editor': typeof AuthedImageEditorRoute
  '/_authed/wizard': typeof AuthedWizardRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/welcome'
    | '/404'
    | '/account-settings'
    | '/analysis-comparison'
    | '/bio-analyzer'
    | '/bio-analyzer-pro'
    | '/dashboard'
    | '/image-analyzer'
    | '/image-analyzer-pro'
    | '/image-editor'
    | '/wizard'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/welcome'
    | '/404'
    | '/account-settings'
    | '/analysis-comparison'
    | '/bio-analyzer'
    | '/bio-analyzer-pro'
    | '/dashboard'
    | '/image-analyzer'
    | '/image-analyzer-pro'
    | '/image-editor'
    | '/wizard'
  id:
    | '__root__'
    | '/'
    | '/welcome'
    | '/_authed/404'
    | '/_authed/account-settings'
    | '/_authed/analysis-comparison'
    | '/_authed/bio-analyzer'
    | '/_authed/bio-analyzer-pro'
    | '/_authed/dashboard'
    | '/_authed/image-analyzer'
    | '/_authed/image-analyzer-pro'
    | '/_authed/image-editor'
    | '/_authed/wizard'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  WelcomeRoute: typeof WelcomeRoute
  Authed404Route: typeof Authed404Route
  AuthedAccountSettingsRoute: typeof AuthedAccountSettingsRoute
  AuthedAnalysisComparisonRoute: typeof AuthedAnalysisComparisonRoute
  AuthedBioAnalyzerRoute: typeof AuthedBioAnalyzerRoute
  AuthedBioAnalyzerProRoute: typeof AuthedBioAnalyzerProRoute
  AuthedDashboardRoute: typeof AuthedDashboardRoute
  AuthedImageAnalyzerRoute: typeof AuthedImageAnalyzerRoute
  AuthedImageAnalyzerProRoute: typeof AuthedImageAnalyzerProRoute
  AuthedImageEditorRoute: typeof AuthedImageEditorRoute
  AuthedWizardRoute: typeof AuthedWizardRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/welcome': {
      id: '/welcome'
      path: '/welcome'
      fullPath: '/welcome'
      preLoaderRoute: typeof WelcomeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/wizard': {
      id: '/_authed/wizard'
      path: '/wizard'
      fullPath: '/wizard'
      preLoaderRoute: typeof AuthedWizardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/image-editor': {
      id: '/_authed/image-editor'
      path: '/image-editor'
      fullPath: '/image-editor'
      preLoaderRoute: typeof AuthedImageEditorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/image-analyzer-pro': {
      id: '/_authed/image-analyzer-pro'
      path: '/image-analyzer-pro'
      fullPath: '/image-analyzer-pro'
      preLoaderRoute: typeof AuthedImageAnalyzerProRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/image-analyzer': {
      id: '/_authed/image-analyzer'
      path: '/image-analyzer'
      fullPath: '/image-analyzer'
      preLoaderRoute: typeof AuthedImageAnalyzerRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/dashboard': {
      id: '/_authed/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthedDashboardRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/bio-analyzer-pro': {
      id: '/_authed/bio-analyzer-pro'
      path: '/bio-analyzer-pro'
      fullPath: '/bio-analyzer-pro'
      preLoaderRoute: typeof AuthedBioAnalyzerProRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/bio-analyzer': {
      id: '/_authed/bio-analyzer'
      path: '/bio-analyzer'
      fullPath: '/bio-analyzer'
      preLoaderRoute: typeof AuthedBioAnalyzerRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/analysis-comparison': {
      id: '/_authed/analysis-comparison'
      path: '/analysis-comparison'
      fullPath: '/analysis-comparison'
      preLoaderRoute: typeof AuthedAnalysisComparisonRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/account-settings': {
      id: '/_authed/account-settings'
      path: '/account-settings'
      fullPath: '/account-settings'
      preLoaderRoute: typeof AuthedAccountSettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/404': {
      id: '/_authed/404'
      path: '/404'
      fullPath: '/404'
      preLoaderRoute: typeof Authed404RouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  WelcomeRoute: WelcomeRoute,
  Authed404Route: Authed404Route,
  AuthedAccountSettingsRoute: AuthedAccountSettingsRoute,
  AuthedAnalysisComparisonRoute: AuthedAnalysisComparisonRoute,
  AuthedBioAnalyzerRoute: AuthedBioAnalyzerRoute,
  AuthedBioAnalyzerProRoute: AuthedBioAnalyzerProRoute,
  AuthedDashboardRoute: AuthedDashboardRoute,
  AuthedImageAnalyzerRoute: AuthedImageAnalyzerRoute,
  AuthedImageAnalyzerProRoute: AuthedImageAnalyzerProRoute,
  AuthedImageEditorRoute: AuthedImageEditorRoute,
  AuthedWizardRoute: AuthedWizardRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

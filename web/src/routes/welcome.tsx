import { createFileRoute } from "@tanstack/react-router";
import { SignIn, SignUp, SignedIn, SignedOut } from "@clerk/tanstack-react-start";
import { Link } from "@tanstack/react-router";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Heart, Sparkles, Star, ArrowLeft } from "lucide-react";
import { useState, useEffect } from "react";
import { WizardContainer } from "@/components/@wizard/wizard-container";

export const Route = createFileRoute("/welcome")({
  component: WelcomeComponent,
});

function WelcomeComponent() {
  const [showSignIn, setShowSignIn] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-hero flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <SignedOut>
          {!showSignIn && !showSignUp && (
            <WelcomeCard
              onSignIn={() => setShowSignIn(true)}
              onSignUp={() => setShowSignUp(true)}
            />
          )}
          {showSignIn && <SignInCard onBack={() => setShowSignIn(false)} />}
          {showSignUp && <SignUpCard onBack={() => setShowSignUp(false)} />}
        </SignedOut>
        <SignedIn>
          <WizardRedirect />
        </SignedIn>
      </div>
    </div>
  );
}

interface WelcomeCardProps {
  onSignIn: () => void;
  onSignUp: () => void;
}

function WelcomeCard({ onSignIn, onSignUp }: WelcomeCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardHeader className="text-center pb-6">
        <div className="flex justify-center mb-4">
          <div className="relative">
            <Heart className="w-12 h-12 text-flame-red fill-current" />
            <Sparkles className="w-4 h-4 text-sparks-pink absolute -top-1 -right-1" />
          </div>
        </div>
        <CardTitle className="text-h2-mobile md:text-h2 text-graphite-90">
          Welcome to TinderOP
        </CardTitle>
        <CardDescription className="text-body-md text-graphite-60">
          The AI-powered dating profile optimizer that helps you get more matches
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center space-x-3 text-sm text-graphite-60">
            <Star className="w-4 h-4 text-sparks-pink" />
            <span>AI-powered photo analysis</span>
          </div>
          <div className="flex items-center space-x-3 text-sm text-graphite-60">
            <Sparkles className="w-4 h-4 text-sparks-pink" />
            <span>Bio optimization suggestions</span>
          </div>
          <div className="flex items-center space-x-3 text-sm text-graphite-60">
            <Heart className="w-4 h-4 text-sparks-pink" />
            <span>Increase your match rate</span>
          </div>
        </div>

        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-graphite-20" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-cloud-white text-graphite-60">Get started</span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <Button variant="primary" className="w-full" onClick={onSignUp}>
              Sign Up
            </Button>
            <Button variant="secondary" className="w-full" onClick={onSignIn}>
              Sign In
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface AuthCardProps {
  onBack: () => void;
}

function SignInCard({ onBack }: AuthCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardHeader className="text-center pb-6">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="text-graphite-60 hover:text-graphite-90"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1 flex justify-center">
            <Heart className="w-8 h-8 text-flame-red fill-current" />
          </div>
          <div className="w-10" />
        </div>
        <CardTitle className="text-h2-mobile md:text-h2 text-graphite-90">Sign In</CardTitle>
        <CardDescription className="text-body-md text-graphite-60">
          Welcome back! Sign in to continue optimizing your profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-center">
          <SignIn
            routing="hash"
            signUpUrl="/welcome"
            afterSignInUrl="/dashboard"
            appearance={{
              elements: {
                rootBox: "w-full",
                card: "shadow-none border-0 bg-transparent",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                socialButtonsBlockButton:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                formButtonPrimary:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                footerActionLink: "text-flame-red hover:text-flame-red/80",
              },
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function SignUpCard({ onBack }: AuthCardProps) {
  return (
    <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
      <CardHeader className="text-center pb-6">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={onBack}
            className="text-graphite-60 hover:text-graphite-90"
          >
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1 flex justify-center">
            <Heart className="w-8 h-8 text-flame-red fill-current" />
          </div>
          <div className="w-10" />
        </div>
        <CardTitle className="text-h2-mobile md:text-h2 text-graphite-90">Sign Up</CardTitle>
        <CardDescription className="text-body-md text-graphite-60">
          Create your account and start getting better matches today
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-center">
          <SignUp
            routing="hash"
            signInUrl="/welcome"
            afterSignUpUrl="/dashboard"
            appearance={{
              elements: {
                rootBox: "w-full",
                card: "shadow-none border-0 bg-transparent",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                socialButtonsBlockButton:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                formButtonPrimary:
                  "bg-gradient-primary text-cloud-white hover:bg-gradient-primary/90",
                footerActionLink: "text-flame-red hover:text-flame-red/80",
              },
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}

function WizardRedirect() {
  const [showWizard, setShowWizard] = useState(false);

  useEffect(() => {
    // Small delay to ensure smooth transition
    const timer = setTimeout(() => {
      setShowWizard(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (!showWizard) {
    return (
      <Card className="shadow-2xl border-0 bg-cloud-white/95 backdrop-blur-sm">
        <CardContent className="p-8 text-center">
          <div className="animate-pulse">
            <Heart className="w-12 h-12 text-flame-red fill-current mx-auto mb-4" />
            <p className="text-graphite-60">Loading your profile setup...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return <WizardContainer />;
}

import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface WizardData {
  // Step 1: Welcome
  hasSeenWelcome?: boolean;
  
  // Step 2: Goals
  primaryGoal?: "more_matches" | "better_conversations" | "serious_relationship" | "casual_dating";
  secondaryGoals?: string[];
  
  // Step 3: Profile Setup
  name?: string;
  age?: number;
  location?: string;
  occupation?: string;
  
  // Step 4: Preferences
  targetAgeRange?: { min: number; max: number };
  targetGender?: "men" | "women" | "everyone";
  relationshipType?: "casual" | "serious" | "open";
  
  // Step 5: Dating Platform
  primaryPlatform?: "tinder" | "bumble" | "hinge" | "other";
  otherPlatforms?: string[];
  
  // Step 6: Preparation
  hasReadTips?: boolean;
  isReadyForAnalysis?: boolean;
  
  // Step 7: Photo Analysis
  uploadedPhotos?: Array<{
    id: string;
    fileName: string;
    preview: string;
    analysisResult?: any;
  }>;
  photoAnalysisCompleted?: boolean;
  
  // Step 8: Bio Optimization
  originalBio?: string;
  improvedBio?: string;
  bioAnalysisResult?: any;
  bioAnalysisCompleted?: boolean;
}

interface WizardStore {
  // State
  currentStep: number;
  wizardData: WizardData;
  completedSteps: Set<number>;
  
  // Actions
  setCurrentStep: (step: number) => void;
  updateWizardData: (data: Partial<WizardData>) => void;
  markStepCompleted: (step: number, stepData?: any) => void;
  isStepCompleted: (step: number) => boolean;
  resetWizard: () => void;
  
  // Navigation helpers
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  canGoToStep: (step: number) => boolean;
}

export const useWizardStore = create<WizardStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentStep: 1,
      wizardData: {},
      completedSteps: new Set<number>(),

      // Actions
      setCurrentStep: (step: number) => {
        console.log(`🧙‍♂️ Wizard: Moving to step ${step}`);
        set({ currentStep: step });
      },

      updateWizardData: (data: Partial<WizardData>) => {
        console.log("🧙‍♂️ Wizard: Updating data", data);
        set((state) => ({
          wizardData: { ...state.wizardData, ...data },
        }));
      },

      markStepCompleted: (step: number, stepData?: any) => {
        console.log(`🧙‍♂️ Wizard: Marking step ${step} as completed`, stepData);
        
        set((state) => {
          const newCompletedSteps = new Set(state.completedSteps);
          newCompletedSteps.add(step);
          
          let updatedWizardData = state.wizardData;
          if (stepData) {
            updatedWizardData = { ...state.wizardData, ...stepData };
          }
          
          return {
            completedSteps: newCompletedSteps,
            wizardData: updatedWizardData,
          };
        });
      },

      isStepCompleted: (step: number) => {
        return get().completedSteps.has(step);
      },

      resetWizard: () => {
        console.log("🧙‍♂️ Wizard: Resetting wizard state");
        set({
          currentStep: 1,
          wizardData: {},
          completedSteps: new Set<number>(),
        });
      },

      // Navigation helpers
      goToNextStep: () => {
        const { currentStep } = get();
        if (currentStep < 8) {
          set({ currentStep: currentStep + 1 });
        }
      },

      goToPreviousStep: () => {
        const { currentStep } = get();
        if (currentStep > 1) {
          set({ currentStep: currentStep - 1 });
        }
      },

      canGoToStep: (step: number) => {
        const { completedSteps } = get();
        // Can go to step if all previous steps are completed
        for (let i = 1; i < step; i++) {
          if (!completedSteps.has(i)) {
            return false;
          }
        }
        return true;
      },
    }),
    {
      name: "tinderop-wizard-storage",
      partialize: (state) => ({
        currentStep: state.currentStep,
        wizardData: state.wizardData,
        completedSteps: Array.from(state.completedSteps), // Convert Set to Array for persistence
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Convert Array back to Set after rehydration
          state.completedSteps = new Set(state.completedSteps as any);
          console.log("🧙‍♂️ Wizard: Rehydrated state", state);
        }
      },
    }
  )
);
